<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Find the DOMINANT Term - Final</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .timer-warning {
            color: #f87171;
            /* red-400 */
        }

        .life-lost {
            animation: screen-flash 0.5s ease-out;
        }

        @keyframes screen-flash {
            0% {
                background-color: #4c0519;
                /* rose-950 */
            }

            100% {
                background-color: #1e293b;
            }

            /* slate-800 */
        }
    </style>
</head>

<body class="bg-slate-800 text-slate-100 flex items-center justify-center h-screen font-sans" id="body">

    <div id="game-container" class="w-full max-w-3xl bg-slate-900 rounded-lg shadow-2xl p-8 border border-cyan-500/50">
        <div class="text-center border-b border-slate-700 pb-4 mb-4">
            <h1 class="text-2xl font-bold text-cyan-400">MISSION: Simplify the Target</h1>
            <p id="narrative" class="text-slate-400 min-h-[40px]">Commander, our mission is to eliminate escort fleets
                to provide clear targeting solutions for the flagship.</p>
        </div>

        <div class="grid grid-cols-3 gap-4 text-center bg-slate-800/50 p-3 rounded-md mb-6">
            <div>LIVES: <span id="lives" class="text-2xl font-bold text-green-400"></span></div>
            <div>SCORE: <span id="score" class="text-2xl font-bold text-green-400">0</span></div>
            <div>TIME: <span id="timer" class="text-2xl font-bold text-cyan-400">--</span></div>
        </div>

        <div id="feedback" class="text-lg text-yellow-400 font-semibold h-8 text-center mb-4">&nbsp;</div>

        <div class="bg-slate-950 p-4 rounded-md mb-6 text-center shadow-inner">
            <h2 class="text-sm uppercase text-slate-500 mb-2">Target Profile</h2>
            <p id="expression" class="text-3xl font-mono text-white"></p>
        </div>

        <div class="mb-6">
            <h2 id="targeting-header" class="text-sm uppercase text-slate-500 mb-2 text-center">Targeting Panel: Escorts
            </h2>
            <div id="targeting-panel" class="grid grid-cols-2 sm:grid-cols-4 gap-4"></div>
        </div>

        <button id="next-round-btn"
            class="hidden w-full bg-green-500 hover:bg-green-400 text-slate-900 font-bold py-3 px-6 rounded-lg">AWAITING
            NEXT TARGET</button>
    </div>

    <div id="game-over-modal" class="hidden fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center">
        <div class="bg-slate-900 border border-red-500/50 p-8 rounded-lg text-center">
            <h2 class="text-4xl font-bold text-red-500 mb-4">MISSION FAILED</h2>
            <p class="text-xl mb-2">Final Score: <span id="final-score" class="text-white font-bold"></span></p>
            <p class="text-lg mb-6">High Score: <span id="high-score" class="text-yellow-400 font-bold"></span></p>
            <button id="play-again-btn"
                class="bg-cyan-500 hover:bg-cyan-400 text-slate-900 font-bold py-3 px-8 rounded-lg">PLAY AGAIN</button>
        </div>
    </div>

    <script>
        // --- DOM ELEMENTS ---
        const bodyEl = document.getElementById('body');
        const scoreEl = document.getElementById('score');
        const livesEl = document.getElementById('lives');
        const timerEl = document.getElementById('timer');
        const feedbackEl = document.getElementById('feedback');
        const expressionEl = document.getElementById('expression');
        const targetingPanelEl = document.getElementById('targeting-panel');
        const targetingHeaderEl = document.getElementById('targeting-header');
        const nextRoundBtn = document.getElementById('next-round-btn');
        const gameOverModal = document.getElementById('game-over-modal');
        const finalScoreEl = document.getElementById('final-score');
        const highScoreEl = document.getElementById('high-score');
        const playAgainBtn = document.getElementById('play-again-btn');
        const narrativeEl = document.getElementById('narrative');

        // --- GAME STATE ---
        let score = 0, lives = 3, currentLevelIndex = 0, timerInterval, timeLeft = 0;
        let highScore = localStorage.getItem('bigOHighScore') || 0;
        let gameInProgress = true;
        let currentLevelData;

        // --- LEVEL GENERATION ---
        function generateLevel(level) {
            const termCount = Math.min(2 + level, 8);
            const maxPower = 1 + Math.floor(level / 2);

            let terms = [];
            const dominantPowerValue = Math.ceil(Math.random() * maxPower) + 1;
            let dominantTermObj = {
                text: "",
                isDominant: true,
                coefficient: Math.floor(Math.random() * 99) + 2,
                variable: `n<sup>${dominantPowerValue}</sup>`
            };
            dominantTermObj.text = `${dominantTermObj.coefficient}${dominantTermObj.variable}`;
            terms.push(dominantTermObj);

            for (let i = 0; i < termCount - 1; i++) {
                let power = Math.floor(Math.random() * dominantPowerValue);
                let coeff = Math.floor(Math.random() * 200) + 1;
                let variable = power > 1 ? `n<sup>${power}</sup>` : (power === 1 ? 'n' : '');
                let text = variable ? `${coeff}${variable}` : `${coeff}`;
                terms.push({ text, isDominant: false });
            }

            terms.sort(() => Math.random() - 0.5);
            const fullString = "T(n) = " + terms.map(t => t.text).join(' + ');

            // FIX 1: Keep the <sup> tags in the answer string.
            const finalAnswer = "O(" + dominantTermObj.variable + ")";

            return { fullString, terms, answer: finalAnswer, time: Math.max(25 - level * 2, 5) };
        }

        // --- GAME LOGIC ---
        function startGame() {
            score = 0; lives = 3; currentLevelIndex = 0; gameInProgress = true;
            updateScore(); updateLives();
            gameOverModal.classList.add('hidden');
            startPhase1(currentLevelIndex);
        }

        function startPhase1(levelIndex) {
            currentLevelData = generateLevel(levelIndex);

            let nonDominantTerms = currentLevelData.terms.filter(t => !t.isDominant);
            let destroyedTermsCount = 0;

            expressionEl.innerHTML = currentLevelData.fullString;
            feedbackEl.textContent = "Awaiting orders...";
            narrativeEl.textContent = "Eliminate the escort fleet before the timer runs out.";
            targetingHeaderEl.textContent = "Targeting Panel: Escorts";
            targetingPanelEl.innerHTML = '';
            targetingPanelEl.className = "grid grid-cols-2 sm:grid-cols-4 gap-4"; // Reset grid
            nextRoundBtn.classList.add('hidden');

            timeLeft = currentLevelData.time;
            updateTimerDisplay();
            clearInterval(timerInterval);
            timerInterval = setInterval(() => {
                timeLeft--;
                updateTimerDisplay();
                if (timeLeft <= 0) loseLife("Enemy found a firing solution!");
            }, 1000);

            currentLevelData.terms.forEach(term => {
                const button = document.createElement('button');
                button.innerHTML = term.text;
                button.className = "w-full p-4 bg-slate-700 hover:bg-cyan-600 rounded-md font-mono text-lg";

                button.addEventListener('click', () => {
                    if (!gameInProgress || button.disabled) return;
                    if (term.isDominant) {
                        loseLife("You fired on the primary target!");
                    } else {
                        feedbackEl.textContent = `Escort ${term.text} eliminated!`;
                        button.disabled = true;
                        button.className = "w-full p-4 bg-slate-800 text-slate-500 line-through rounded-md font-mono text-lg";
                        score++;
                        updateScore();
                        destroyedTermsCount++;
                        if (destroyedTermsCount === nonDominantTerms.length) {
                            completePhase1();
                        }
                    }
                });
                targetingPanelEl.appendChild(button);
            });
        }

        function completePhase1() {
            clearInterval(timerInterval);
            score += timeLeft; // Time bonus
            updateScore();
            feedbackEl.textContent = "Escort fleet eliminated! Time bonus awarded.";
            setTimeout(startPhase2, 1500);
        }

        function startPhase2() {
            const dominantTerm = currentLevelData.terms.find(t => t.isDominant);

            narrativeEl.textContent = "Final decision, Commander. Isolate the constant to finalise the targeting solution.";
            targetingHeaderEl.textContent = "Targeting Panel: Finalise Solution";
            feedbackEl.textContent = "Which part do we drop?";
            targetingPanelEl.innerHTML = '';
            targetingPanelEl.className = "grid grid-cols-2 gap-4"; // Set 2-col grid for the choice

            if (dominantTerm.coefficient === 1) {
                feedbackEl.textContent = "No constant to remove. Solution is clean!";
                setTimeout(endRound, 1500);
                return;
            }

            const constantButton = document.createElement('button');
            constantButton.innerHTML = `Target : ${dominantTerm.coefficient}`;
            constantButton.className = "w-full p-4 bg-yellow-600 hover:bg-yellow-500 rounded-md font-mono text-lg";
            constantButton.addEventListener('click', () => {
                feedbackEl.textContent = `Constant ${dominantTerm.coefficient} neutralised!`;
                constantButton.disabled = true;
                variableButton.disabled = true;
                constantButton.className = "w-full p-4 bg-slate-800 text-slate-500 line-through rounded-md font-mono text-lg";
                score += 5;
                updateScore();
                endRound();
            });

            const variableButton = document.createElement('button');
            variableButton.innerHTML = `Target : ${dominantTerm.variable}`;
            variableButton.className = "w-full p-4 bg-slate-700 hover:bg-red-600 rounded-md font-mono text-lg";
            variableButton.addEventListener('click', () => {
                loseLife("That's the core complexity! Wrong target!");
            });

            targetingPanelEl.appendChild(constantButton);
            targetingPanelEl.appendChild(variableButton);
        }

        function endRound() {
            score += 10; // Round clear bonus
            updateScore();
            const dominantTerm = currentLevelData.terms.find(t => t.isDominant);

            expressionEl.innerHTML = `Target simplified to: <span class="text-cyan-400 font-bold">${dominantTerm.variable}</span>`;

            feedbackEl.innerHTML = `Targeting solution locked! The core complexity is ${currentLevelData.answer}.`;

            nextRoundBtn.classList.remove('hidden');
        }

        function loseLife(reason) {
            if (!gameInProgress) return;
            clearInterval(timerInterval);
            lives--;
            updateLives();
            feedbackEl.textContent = reason;
            bodyEl.classList.add('life-lost');
            setTimeout(() => bodyEl.classList.remove('life-lost'), 500);

            if (lives <= 0) {
                gameOver();
            } else {
                setTimeout(() => startPhase1(currentLevelIndex), 2000);
            }
        }

        function gameOver() {
            gameInProgress = false;
            if (score > highScore) {
                highScore = score;
                localStorage.setItem('bigOHighScore', highScore);
            }
            finalScoreEl.textContent = score;
            highScoreEl.textContent = highScore;
            gameOverModal.classList.remove('hidden');
        }

        function updateScore() { scoreEl.textContent = score; }
        function updateTimerDisplay() {
            timerEl.textContent = timeLeft;
            timerEl.classList.toggle('timer-warning', timeLeft <= 5);
        }
        function updateLives() { livesEl.innerHTML = '❤️'.repeat(lives); }

        nextRoundBtn.addEventListener('click', () => {
            currentLevelIndex++;
            startPhase1(currentLevelIndex);
        });
        playAgainBtn.addEventListener('click', startGame);

        startGame();
    </script>
</body>

</html>